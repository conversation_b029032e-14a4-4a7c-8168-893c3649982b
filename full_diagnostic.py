#!/usr/bin/env python3
"""
Comprehensive POS System Diagnostic Tool
Check all possible issues with categories, products, and system functionality
"""

import sys
import traceback
import sqlite3
import os

def check_database():
    """Check database structure and data"""
    print("🔍 CHECKING DATABASE...")
    
    try:
        from database import get_db_connection
        conn = get_db_connection()
        c = conn.cursor()
        
        # Check if database file exists
        if not os.path.exists('pos_system.db'):
            print("❌ Database file 'pos_system.db' not found!")
            return False
        
        print("✅ Database file exists")
        
        # Check tables exist
        c.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in c.fetchall()]
        required_tables = ['categories', 'products', 'users', 'sales']
        
        for table in required_tables:
            if table in tables:
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing!")
                return False
        
        # Check data counts
        c.execute("SELECT COUNT(*) FROM categories")
        cat_count = c.fetchone()[0]
        print(f"📊 Categories: {cat_count}")
        
        c.execute("SELECT COUNT(*) FROM products")
        prod_count = c.fetchone()[0]
        print(f"📊 Products: {prod_count}")
        
        c.execute("SELECT COUNT(*) FROM users")
        user_count = c.fetchone()[0]
        print(f"📊 Users: {user_count}")
        
        # Show actual data
        if cat_count > 0:
            print("\n📋 CATEGORIES:")
            c.execute("SELECT id, name FROM categories")
            for row in c.fetchall():
                print(f"  - ID: {row[0]}, Name: '{row[1]}'")
        else:
            print("⚠️ No categories found!")
            
        if prod_count > 0:
            print("\n📋 PRODUCTS:")
            c.execute("SELECT id, name, category_id, price FROM products")
            for row in c.fetchall():
                print(f"  - ID: {row[0]}, Name: '{row[1]}', Category: {row[2]}, Price: {row[3]}")
        else:
            print("⚠️ No products found!")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        traceback.print_exc()
        return False

def check_cache_functions():
    """Check cached database functions"""
    print("\n🔍 CHECKING CACHE FUNCTIONS...")
    
    try:
        from database import get_categories_cached, get_products_cached, get_products_by_category_cached
        
        # Test categories cache
        print("Testing get_categories_cached()...")
        categories = get_categories_cached()
        print(f"✅ Categories cache returned {len(categories)} items")
        
        # Test products cache
        print("Testing get_products_cached()...")
        products = get_products_cached()
        print(f"✅ Products cache returned {len(products)} items")
        
        # Test products by category
        if categories:
            cat_id = categories[0]['id']
            print(f"Testing get_products_by_category_cached({cat_id})...")
            cat_products = get_products_by_category_cached(cat_id)
            print(f"✅ Category products returned {len(cat_products)} items")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache functions error: {e}")
        traceback.print_exc()
        return False

def check_pos_screen_imports():
    """Check if POS screen can import properly"""
    print("\n🔍 CHECKING POS SCREEN IMPORTS...")
    
    try:
        from pos_screen import POSScreen
        print("✅ POSScreen import successful")
        
        # Check if it can import database functions
        from database import get_categories_cached, get_products_by_category_cached
        print("✅ Database function imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def check_tkinter():
    """Check if tkinter is working"""
    print("\n🔍 CHECKING TKINTER...")
    
    try:
        import tkinter as tk
        print("✅ Tkinter import successful")
        
        # Try creating a simple window
        root = tk.Tk()
        root.withdraw()  # Hide it
        print("✅ Tkinter window creation successful")
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Tkinter error: {e}")
        traceback.print_exc()
        return False

def simulate_pos_loading():
    """Simulate the POS screen loading process"""
    print("\n🔍 SIMULATING POS LOADING PROCESS...")
    
    try:
        # Simulate what happens in pos_screen.py load_categories
        print("Step 1: Import cache function...")
        from database import get_categories_cached
        
        print("Step 2: Call get_categories_cached()...")
        categories = get_categories_cached()
        print(f"✅ Got {len(categories)} categories")
        
        if categories:
            print("Step 3: Process first category...")
            cat = categories[0]
            print(f"  Category: {cat}")
            
            print("Step 4: Test product loading for category...")
            from database import get_products_by_category_cached
            products = get_products_by_category_cached(cat['id'])
            print(f"✅ Got {len(products)} products for category {cat['id']}")
            
            if products:
                print("Step 5: Process first product...")
                prod = products[0]
                print(f"  Product: {prod}")
        
        return True
        
    except Exception as e:
        print(f"❌ POS loading simulation failed: {e}")
        traceback.print_exc()
        return False

def check_display_settings():
    """Check display settings"""
    print("\n🔍 CHECKING DISPLAY SETTINGS...")
    
    try:
        from database import get_display_settings
        settings = get_display_settings()
        print(f"✅ Display settings: {settings}")
        return True
        
    except Exception as e:
        print(f"❌ Display settings error: {e}")
        traceback.print_exc()
        return False

def check_file_permissions():
    """Check file permissions"""
    print("\n🔍 CHECKING FILE PERMISSIONS...")
    
    files_to_check = ['pos_system.db', 'database.py', 'pos_screen.py', 'main.py']
    
    for file in files_to_check:
        if os.path.exists(file):
            if os.access(file, os.R_OK):
                print(f"✅ {file} - readable")
            else:
                print(f"❌ {file} - not readable")
                
            if file.endswith('.db'):
                if os.access(file, os.W_OK):
                    print(f"✅ {file} - writable")
                else:
                    print(f"❌ {file} - not writable")
        else:
            print(f"❌ {file} - not found")

def main():
    """Run comprehensive diagnostics"""
    print("🏥 POS SYSTEM COMPREHENSIVE DIAGNOSTIC")
    print("=" * 50)
    
    all_passed = True
    
    # Run all checks
    checks = [
        ("Database Structure & Data", check_database),
        ("Cache Functions", check_cache_functions),
        ("POS Screen Imports", check_pos_screen_imports),
        ("Tkinter GUI", check_tkinter),
        ("POS Loading Simulation", simulate_pos_loading),
        ("Display Settings", check_display_settings),
        ("File Permissions", check_file_permissions),
    ]
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} failed with exception: {e}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("The POS system should be working correctly.")
    else:
        print("❌ SOME CHECKS FAILED!")
        print("Issues found that need to be fixed.")
    
    print("\n💡 If categories/products still don't show:")
    print("1. Try restarting the POS system")
    print("2. Check if you're clicking on a category button")
    print("3. Look for any error messages in the console")
    print("4. Verify you have data in the database")

if __name__ == '__main__':
    main()
