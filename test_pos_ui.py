#!/usr/bin/env python3
"""
Test POS UI initialization
"""

import tkinter as tk
import sys

def test_pos_screen_creation():
    """Test creating POS screen"""
    print("🔍 Testing POS screen creation...")
    
    try:
        # Create a minimal app mock
        class MockApp:
            def __init__(self):
                self.root = tk.Tk()
                self.root.withdraw()  # Hide window
                self.current_user = {'username': 'test', 'is_admin': True}
                self.cart_items = []
                
            def get_text(self, key):
                return key.replace('_', ' ').title()
        
        app = MockApp()
        
        # Try to create POS screen
        from pos_screen import POSScreen
        pos_screen = POSScreen(app)
        
        print("✅ POSScreen object created successfully")
        
        # Try to create the UI
        pos_screen.create_ui()
        print("✅ POS UI created successfully")
        
        # Check if categories frame exists
        if hasattr(pos_screen, 'categories_frame') and pos_screen.categories_frame:
            print("✅ Categories frame created")
            
            # Check if any category buttons were created
            children = pos_screen.categories_frame.winfo_children()
            print(f"✅ Categories frame has {len(children)} child widgets")
            
            if len(children) > 0:
                print("✅ Category buttons were created!")
                for i, child in enumerate(children):
                    if hasattr(child, 'cget'):
                        try:
                            text = child.cget('text')
                            print(f"  Button {i+1}: '{text}'")
                        except:
                            print(f"  Widget {i+1}: {type(child).__name__}")
            else:
                print("❌ No category buttons found!")
        else:
            print("❌ Categories frame not created")
        
        # Check products frame
        if hasattr(pos_screen, 'products_frame') and pos_screen.products_frame:
            print("✅ Products frame created")
        else:
            print("❌ Products frame not created")
        
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ POS screen creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_category_button_creation():
    """Test category button creation specifically"""
    print("\n🔍 Testing category button creation...")
    
    try:
        # Create minimal test environment
        root = tk.Tk()
        root.withdraw()
        
        # Create a test frame
        test_frame = tk.Frame(root)
        
        # Get categories
        from database import get_categories_cached
        categories = get_categories_cached()
        
        print(f"Found {len(categories)} categories to create buttons for")
        
        # Try to create buttons like POS screen does
        for i, category in enumerate(categories):
            print(f"Creating button for category: {category['name']}")
            
            if category['image']:
                print(f"  Category has image data")
                try:
                    # Try to load image
                    from PIL import Image, ImageTk
                    import io
                    
                    image_data = category['image']
                    image = Image.open(io.BytesIO(image_data))
                    image = image.resize((48, 48), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)
                    
                    # Create button with image
                    cat_btn = tk.Button(test_frame, text=category['name'],
                                       image=photo, compound=tk.TOP,
                                       font=('Segoe UI', 10), bg='#2d2d2d', fg='white',
                                       width=162, height=90, relief='raised', bd=1)
                    cat_btn.image = photo  # Keep reference
                    print(f"  ✅ Image button created successfully")
                    
                except Exception as e:
                    print(f"  ❌ Image loading failed: {e}")
                    # Fallback to text button
                    cat_btn = tk.Button(test_frame, text=category['name'],
                                       font=('Segoe UI', 11), bg='#2d2d2d', fg='white',
                                       width=20, height=3, relief='raised', bd=1)
                    print(f"  ✅ Text button created as fallback")
            else:
                # Text-only button
                cat_btn = tk.Button(test_frame, text=category['name'],
                                   font=('Segoe UI', 11), bg='#2d2d2d', fg='white',
                                   width=20, height=3, relief='raised', bd=1)
                print(f"  ✅ Text button created")
            
            cat_btn.pack(fill=tk.X, padx=8, pady=3)
        
        # Check how many buttons were created
        children = test_frame.winfo_children()
        print(f"✅ Created {len(children)} category buttons successfully")
        
        root.destroy()
        return len(children) > 0
        
    except Exception as e:
        print(f"❌ Category button creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run UI tests"""
    print("🖥️ POS UI TESTS")
    print("=" * 40)
    
    tests = [
        ("POS Screen Creation", test_pos_screen_creation),
        ("Category Button Creation", test_category_button_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*40}")
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL UI TESTS PASSED! The POS screen should work correctly.")
        print("\n💡 If categories still don't show in the actual POS:")
        print("1. Make sure you're looking in the categories panel (left side)")
        print("2. Try scrolling in the categories area")
        print("3. Check if the window is maximized properly")
        print("4. Look for any error messages in the console")
    else:
        print("❌ Some UI tests failed. There are display issues.")

if __name__ == '__main__':
    main()
