#!/usr/bin/env python3
"""
Test POS screen loading without GUI
"""

def test_category_loading():
    """Test category loading logic"""
    print("🔍 Testing category loading...")
    
    try:
        from database import get_categories_cached
        categories = get_categories_cached()
        
        print(f"✅ Found {len(categories)} categories")
        
        for cat in categories:
            print(f"  - ID: {cat['id']}, Name: '{cat['name']}', Has Image: {cat['image'] is not None}")
            
        return len(categories) > 0
        
    except Exception as e:
        print(f"❌ Category loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_loading():
    """Test product loading logic"""
    print("\n🔍 Testing product loading...")
    
    try:
        from database import get_categories_cached, get_products_by_category_cached
        
        categories = get_categories_cached()
        if not categories:
            print("❌ No categories found for product testing")
            return False
            
        cat_id = categories[0]['id']
        print(f"Testing products for category ID: {cat_id}")
        
        products = get_products_by_category_cached(cat_id)
        print(f"✅ Found {len(products)} products in category {cat_id}")
        
        for prod in products:
            print(f"  - ID: {prod['id']}, Name: '{prod['name']}', Price: {prod['price']}, Has Image: {prod['image'] is not None}")
            
        return True
        
    except Exception as e:
        print(f"❌ Product loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pos_screen_simulation():
    """Simulate POS screen category/product loading"""
    print("\n🔍 Simulating POS screen loading...")
    
    try:
        # Simulate what happens in pos_screen.py load_categories
        print("Step 1: Loading categories...")
        from database import get_categories_cached
        categories = get_categories_cached()
        
        if not categories:
            print("❌ No categories to display")
            return False
            
        print(f"✅ Would create {len(categories)} category buttons")
        
        # Simulate clicking on first category
        first_category = categories[0]
        print(f"Step 2: Simulating click on category '{first_category['name']}' (ID: {first_category['id']})")
        
        # Simulate product loading
        from database import get_products_by_category_cached
        products = get_products_by_category_cached(first_category['id'])
        
        print(f"✅ Would create {len(products)} product buttons")
        
        if products:
            print("Sample product buttons that would be created:")
            for i, product in enumerate(products[:3]):  # Show first 3
                button_text = f"{product['name']}\n{product['price']:.2f} MAD"
                print(f"  Button {i+1}: '{button_text}'")
        
        return True
        
    except Exception as e:
        print(f"❌ POS simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 POS LOADING TESTS")
    print("=" * 40)
    
    tests = [
        ("Category Loading", test_category_loading),
        ("Product Loading", test_product_loading),
        ("POS Screen Simulation", test_pos_screen_simulation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*40}")
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Categories and products should display correctly.")
    else:
        print("❌ Some tests failed. There are issues that need fixing.")

if __name__ == '__main__':
    main()
