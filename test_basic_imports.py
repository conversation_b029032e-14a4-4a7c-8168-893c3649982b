#!/usr/bin/env python3
"""
Test basic imports to find where the system is hanging
"""

import sys
import os

print("🔍 Starting basic import tests...")

try:
    print("1. Testing sys and os... ✅")
    
    print("2. Testing tkinter...")
    import tkinter as tk
    print("   tkinter imported ✅")
    
    print("3. Testing PIL...")
    from PIL import Image, ImageTk
    print("   PIL imported ✅")
    
    print("4. Testing database...")
    from database import get_db_connection
    print("   database imported ✅")
    
    print("5. Testing database connection...")
    conn = get_db_connection()
    conn.close()
    print("   database connection works ✅")
    
    print("6. Testing pos_app import...")
    from pos_app import POSApplication
    print("   pos_app imported ✅")
    
    print("7. Testing POSApplication creation...")
    # Don't actually create it, just test if we can
    print("   POSApplication class available ✅")
    
    print("\n🎉 ALL BASIC IMPORTS SUCCESSFUL!")
    print("The issue is likely in the application startup or GUI creation.")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
